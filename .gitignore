# Garmin Connect IQ - Build Output
bin/
*.prg
*.debug.xml

# Generated Files
gen/
*.mcgen

# Intermediate Files
*.mir
*.debug
*.elf

# Build Artifacts
build/
output/
dist/

# IDE Files
.vscode/settings.json
.vscode/launch.json
.vscode/tasks.json
.vscode/.ropeproject/

# OS Generated Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# Windows
*.tmp
*.temp
~$*

# Logs
*.log
debug.log
error.log

# Temporary Files
*.bak
*.swp
*.swo
*~

# Node.js (if using any build tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python (if using any build scripts)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/

# Java (if using any build tools)
*.class
*.jar
*.war
*.ear
target/

# Simulator Files
simulator/
*.sim

# Connect IQ Specific
*.iq
*.store
*.debug.xml

# Personal Configuration
config.json
settings.json
user.properties

# Test Files
test/
tests/
*.test
*_test.*

# Documentation Build
docs/_build/
site/

# Backup Files
*.backup
backup/

# Archive Files
*.zip
*.tar.gz
*.rar
*.7z

# Certificate Files (Keep these secure!)
*.p12
*.pem
*.key
developer_key*

# Environment Variables
.env
.env.local
.env.development
.env.test
.env.production

# Cache
.cache/
*.cache

# Garmin Express
GarminExpress/

# Connect IQ Store
store/
*.store.xml

# Device Specific
device/
devices/

# Profiling
*.prof
profile/

# Coverage Reports
coverage/
*.coverage
.nyc_output/

# Dependency Directories
lib/
libs/
vendor/

# Lock Files
package-lock.json
yarn.lock
Pipfile.lock

# IDE Workspace
*.code-workspace
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# JetBrains
.idea/
*.iml
*.ipr
*.iws
out/

# NetBeans
nbproject/private/
build/
nbbuild/
dist/
nbdist/
.nb-gradle/

# Eclipse
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders
.project
.classpath

# Visual Studio
.vs/
*.user
*.suo
*.userosscache
*.sln.docstates
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
build/
bld/
[Bb]in/
[Oo]bj/

# Keep Important Files (Negation)
!manifest.xml
!monkey.jungle
!resources/
!source/
!README.md
!LICENSE
!.gitignore
