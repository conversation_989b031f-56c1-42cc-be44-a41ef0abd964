# Debug Guide - Madre Watchface

## 🐛 Debugging Flow

### 1. **Enable Debug Output**
Debug output sudah ditambahkan di key points:
- `MadreView.initialize()`
- `MadreView.onLayout()`
- `MadreView.onUpdate()`
- `MadreView.drawMainTime()`

### 2. **Viewing Debug Output**

#### **VS Code Debug Console**
1. Run watchface dengan F5 ("Run Without Debugging")
2. Buka Debug Console: `View` → `Debug Console`
3. Debug output akan muncul dengan format: `[DEBUG] message`

#### **Connect IQ Simulator Console**
1. Buka Connect IQ Simulator
2. Lihat console output di bagian bawah simulator

### 3. **Debug Points yang Sudah Ada**

#### **Application Startup**
```
[DEBUG] MadreView.initialize() - START
[DEBUG] MadreView.initialize() - <PERSON><PERSON>
```

#### **Layout Initialization**
```
[DEBUG] Screen: 416x416
```

#### **Update Cycle**
```
[DEBUG] MadreView.onUpdate() - START
[DEBUG] LayoutManager is null - returning  (jika ada masalah)
[DEBUG] Drawing watchface elements, isAwake: true
```

#### **Time Drawing**
```
[DEBUG] drawMainTime() - START
[DEBUG] Time: 10:08 at position [208, 208]
[DEBUG] drawMainTime() - END
```

## 🔍 Adding More Debug Points

### **Method Entry/Exit**
```monkey-c
function myMethod() {
    System.println("[DEBUG] myMethod() - START");
    
    // Your code here
    
    System.println("[DEBUG] myMethod() - END");
}
```

### **Variable Values**
```monkey-c
var myVar = getSomeValue();
System.println("[DEBUG] myVar = " + (myVar != null ? myVar.toString() : "null"));
```

### **Position/Layout Debug**
```monkey-c
var pos = _layoutManager.getSomePosition();
System.println("[DEBUG] Position: [" + pos[0] + ", " + pos[1] + "]");
```

### **Data Debug**
```monkey-c
var heartRate = DataManager.getHeartRate();
System.println("[DEBUG] Heart Rate: " + (heartRate != null ? heartRate : "null"));
```

### **Color Debug**
```monkey-c
System.println("[DEBUG] Color: 0x" + _accentColor.format("%06X"));
```

## 🚨 Common Issues & Debug

### **Issue: Watchface tidak muncul**
**Debug Steps:**
1. Check initialization:
   ```
   [DEBUG] MadreView.initialize() - START
   [DEBUG] MadreView.initialize() - END
   ```

2. Check layout:
   ```
   [DEBUG] Screen: 416x416
   ```

3. Check update cycle:
   ```
   [DEBUG] MadreView.onUpdate() - START
   ```

**Possible Causes:**
- Exception during initialization
- LayoutManager not created
- Drawing method crashes

### **Issue: Data tidak muncul**
**Debug Steps:**
1. Add debug di DataManager methods:
   ```monkey-c
   static function getTimeString() as String {
       var clockTime = System.getClockTime();
       var timeString = Lang.format("$1$:$2$", [
           clockTime.hour.format("%02d"),
           clockTime.min.format("%02d")
       ]);
       System.println("[DEBUG] DataManager.getTimeString(): " + timeString);
       return timeString;
   }
   ```

2. Check if data is null:
   ```monkey-c
   var steps = DataManager.getSteps();
   System.println("[DEBUG] Steps: " + (steps != null ? steps : "null"));
   ```

### **Issue: Layout tidak sesuai**
**Debug Steps:**
1. Add debug di LayoutManager:
   ```monkey-c
   function getTimePosition() as Array {
       var pos = [_centerX, _centerY];
       System.println("[DEBUG] Time position: [" + pos[0] + ", " + pos[1] + "]");
       return pos;
   }
   ```

2. Check screen dimensions:
   ```monkey-c
   System.println("[DEBUG] Screen: " + _screenWidth + "x" + _screenHeight);
   System.println("[DEBUG] Center: [" + _centerX + ", " + _centerY + "]");
   ```

## 📊 Performance Debug

### **Timing Methods**
```monkey-c
function onUpdate(dc as Dc) as Void {
    var startTime = System.getTimer();
    
    // Your drawing code here
    
    var endTime = System.getTimer();
    System.println("[DEBUG] onUpdate took: " + (endTime - startTime) + "ms");
}
```

### **Memory Usage** (if available)
```monkey-c
if (System has :getSystemStats) {
    var stats = System.getSystemStats();
    if (stats.has(:usedMemory)) {
        System.println("[DEBUG] Memory: " + stats[:usedMemory] + " bytes");
    }
}
```

## 🛠️ Advanced Debugging

### **Exception Handling**
```monkey-c
function drawSomething(dc as Graphics.Dc) as Void {
    try {
        // Your drawing code
        dc.drawText(x, y, font, text, justification);
    } catch (ex) {
        System.println("[ERROR] Drawing failed: " + ex.getErrorMessage());
    }
}
```

### **Conditional Debug**
```monkey-c
// Add debug flag
private var _debugEnabled as Boolean = true;

private function debugLog(message as String) as Void {
    if (_debugEnabled) {
        System.println("[DEBUG] " + message);
    }
}
```

### **Debug Different States**
```monkey-c
function onUpdate(dc as Dc) as Void {
    debugLog("onUpdate - isAwake: " + _isAwake);
    
    if (_isAwake) {
        debugLog("Drawing full watchface");
        // Draw all elements
    } else {
        debugLog("Drawing minimal watchface");
        // Draw only essential elements
    }
}
```

## 📝 Debug Checklist

When debugging issues, check these in order:

- [ ] **Initialization**: Are all objects created properly?
- [ ] **Layout**: Are positions calculated correctly?
- [ ] **Data**: Are all data sources returning valid values?
- [ ] **Drawing**: Are drawing operations successful?
- [ ] **Colors**: Are colors set correctly?
- [ ] **Fonts**: Are fonts available and valid?
- [ ] **Screen**: Are coordinates within screen bounds?
- [ ] **Performance**: Is update cycle fast enough?

## 🎯 Debug Output Examples

### **Successful Run**
```
[DEBUG] MadreView.initialize() - START
[DEBUG] MadreView.initialize() - END
[DEBUG] Screen: 416x416
[DEBUG] MadreView.onUpdate() - START
[DEBUG] Drawing watchface elements, isAwake: true
[DEBUG] drawMainTime() - START
[DEBUG] Time: 10:08 at position [208, 208]
[DEBUG] drawMainTime() - END
```

### **Problem Run**
```
[DEBUG] MadreView.initialize() - START
[DEBUG] MadreView.initialize() - END
[DEBUG] Screen: 416x416
[DEBUG] MadreView.onUpdate() - START
[DEBUG] LayoutManager is null - returning
```
→ **Issue**: LayoutManager not initialized properly

## 🔧 Using DebugUtils.mc

The project includes `DebugUtils.mc` with advanced debugging features:

```monkey-c
// Enable/disable debug
DebugUtils.DEBUG_ENABLED = true;

// Log with levels
DebugUtils.info("MadreView", "Initialization complete");
DebugUtils.warn("DataManager", "Heart rate data not available");
DebugUtils.error("LayoutManager", "Invalid screen dimensions");

// Log variables
DebugUtils.logVar("MadreView", "heartRate", heartRate);
DebugUtils.logArray("LayoutManager", "timePos", timePos);
DebugUtils.logDict("DataManager", "stats", stats);

// Performance timing
DebugUtils.startTimer("onUpdate");
// ... your code ...
DebugUtils.endTimer("onUpdate");
```

**Note**: Currently DebugUtils requires proper import setup. Use simple `System.println()` for immediate debugging.
