# Madre Watchface - Garmin Connect IQ

Modern smartwatch face untuk Garmin devices dengan tampilan yang elegan dan informatif.

## 📱 Preview

Watchface ini menampilkan:
- **Time**: Jam besar di tengah layar
- **Day**: <PERSON> dengan background diagonal stripes
- **Heart Rate**: Lingkaran dengan nilai heart rate
- **Date**: Tanggal di bawah jam
- **Activity Data**: Steps, Calories, Messages
- **Battery**: Indikator battery dengan persentase
- **Bluetooth**: Status koneksi bluetooth

## 🏗️ Project Structure

```
Madre/
├── source/
│   ├── MadreApp.mc          # Main application entry point
│   ├── MadreView.mc         # Main watchface view
│   ├── MadreBackground.mc   # Custom background drawable
│   ├── DataManager.mc       # Data fetching and formatting
│   ├── LayoutManager.mc     # Screen layout and positioning
│   └── UIComponents.mc      # Reusable UI components
├── resources/
│   ├── drawables/
│   │   ├── drawables.xml    # Drawable resources definition
│   │   └── launcher_icon.svg # App launcher icon
│   ├── layouts/
│   │   └── layout.xml       # Layout definition
│   └── strings/
│       └── strings.xml      # String resources
├── bin/                     # Build output (auto-generated)
├── manifest.xml             # App manifest
└── monkey.jungle            # Build configuration
```

## 🔄 Application Flow

### 1. **Application Startup**
```
MadreApp.initialize()
├── AppBase.initialize()
└── Setup application state
```

### 2. **View Initialization**
```
MadreApp.getInitialView()
├── Create MadreView instance
└── Return view array
```

### 3. **Watchface Lifecycle**
```
MadreView Lifecycle:
├── initialize()
│   └── WatchFace.initialize()
├── onLayout(dc)
│   ├── Get screen dimensions
│   └── Initialize LayoutManager
├── onShow()
│   └── Prepare resources
├── onUpdate(dc)
│   ├── Clear screen
│   ├── Draw main time
│   └── Draw additional info (if awake)
├── onHide()
│   └── Cleanup resources
├── onExitSleep()
│   ├── Set _isAwake = true
│   └── Request update
└── onEnterSleep()
    ├── Set _isAwake = false
    └── Request update
```

### 4. **Drawing Flow (onUpdate)**
```
onUpdate(dc) Flow:
├── Clear screen with background color
├── Check if LayoutManager is initialized
├── drawMainTime(dc)
│   ├── Get time from DataManager
│   ├── Get position from LayoutManager
│   └── Draw text to screen
└── If watch is awake:
    ├── drawDayIndicator(dc)
    ├── drawHeartRateIndicator(dc)
    ├── drawDateDisplay(dc)
    ├── drawActivityData(dc)
    ├── drawBatteryIndicator(dc)
    └── drawBluetoothIndicator(dc)
```

## 🧩 Component Details

### **MadreView.mc** - Main Watchface
- Extends `WatchUi.WatchFace`
- Manages watchface lifecycle
- Coordinates all drawing operations
- Handles sleep/wake states

### **DataManager.mc** - Data Layer
```
DataManager Functions:
├── getTimeString() → "10:08"
├── getDateString() → "JUN 8"
├── getDayString() → "WED"
├── getHeartRate() → 100 (bpm)
├── getSteps() → 12570
├── getCalories() → 1380
├── getNotificationCount() → 99
├── getBatteryLevel() → 85.5 (%)
└── isBluetoothConnected() → true/false
```

### **LayoutManager.mc** - Layout System
```
LayoutManager Functions:
├── getTimePosition() → [x, y]
├── getDayPosition() → {:x, :y, :width, :height, :textX, :textY}
├── getHeartRatePosition() → {:centerX, :centerY, :radius, :textX, :textY}
├── getDatePosition() → [x, y]
├── getStepsPosition() → {:x, :y, :iconX, :iconY}
├── getCaloriesPosition() → {:x, :y, :iconX, :iconY}
├── getMessagesPosition() → {:badgeX, :badgeY}
├── getBatteryPosition() → {:x, :y, :width, :height, :textX, :textY}
└── getBluetoothPosition() → {:x, :y, :size}
```

### **UIComponents.mc** - UI Elements
```
UIComponents Functions:
├── drawDiagonalStripes() → Background pattern
├── drawCircularProgress() → Heart rate circle
├── drawBatteryIndicator() → Battery icon
├── drawBluetoothIndicator() → Bluetooth icon
└── drawNotificationBadge() → Message badge
```

## 🐛 Debug Guide

Debug output sudah ditambahkan di key points aplikasi. Lihat `DEBUG_GUIDE.md` untuk panduan lengkap.

### **Quick Debug**
```monkey-c
// Add to any function for debugging
System.println("[DEBUG] Function called with value: " + value);
```

### **Viewing Debug Output**
- **VS Code**: Debug Console (`View` → `Debug Console`)
- **Simulator**: Console output di Connect IQ Simulator

### **Debug Points yang Sudah Ada**
- Application initialization
- Screen layout setup
- Update cycle
- Time drawing
- Data fetching

### **Debug Files**
- `DEBUG_GUIDE.md` - Comprehensive debugging guide
- `source/DebugUtils.mc` - Advanced debug utilities

## 🚀 Build & Run

### **Prerequisites**
- Visual Studio Code
- Connect IQ SDK
- Connect IQ Extension for VS Code

### **Development**
```bash
# Open project in VS Code
code .

# Build for device
Ctrl+Shift+P → "Monkey C: Build for Device"

# Run in simulator
F5 atau "Run Without Debugging"
```

### **Target Devices**
- Garmin Instinct 3 AMOLED 50mm
- (Dapat ditambahkan device lain di manifest.xml)

## 🎨 Customization

### **Colors**
```monkey-c
// In MadreView.mc
private var _backgroundColor as Number = 0x000000;  // Black
private var _foregroundColor as Number = 0xFFFFFF;  // White
private var _accentColor as Number = 0xFF1493;      // Deep Pink
private var _secondaryColor as Number = 0x808080;   // Gray
```

### **Fonts**
```monkey-c
// In MadreView.mc
private var _timeFont as Graphics.FontDefinition = Graphics.FONT_LARGE;
private var _dataFont as Graphics.FontDefinition = Graphics.FONT_MEDIUM;
private var _smallFont as Graphics.FontDefinition = Graphics.FONT_SMALL;
```

### **Layout Adjustments**
Edit `LayoutManager.mc` untuk mengubah posisi elemen.

## 📝 Development Notes

- **Fonts**: Menggunakan font default Garmin (FONT_LARGE, FONT_MEDIUM, FONT_SMALL)
- **Icons**: Menggunakan teks sederhana karena emoji tidak didukung
- **Data**: Semua data diambil melalui DataManager untuk konsistensi
- **Layout**: Responsive design melalui LayoutManager
- **Performance**: Optimized untuk battery life dengan sleep/wake states

## 🔧 Troubleshooting

### **Watchface tidak muncul**
1. Check console untuk error messages
2. Pastikan semua dependencies tersedia
3. Verify target device compatibility

### **Data tidak muncul**
1. Check DataManager functions
2. Verify permissions di manifest.xml
3. Test di real device vs simulator

### **Layout tidak sesuai**
1. Check LayoutManager calculations
2. Verify screen dimensions
3. Test di berbagai ukuran layar
