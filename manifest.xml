<?xml version="1.0"?>
<!-- This is a generated file. It is highly recommended that you DO NOT edit this file. -->
<iq:manifest version="3" xmlns:iq="http://www.garmin.com/xml/connectiq">
    <!--
        Use "Monkey C: Edit Application" from the Visual Studio Code command palette
        to update the application attributes.
    -->
    <iq:application id="eb524bae-1aea-4ae1-b7fd-617e34bef473" type="watchface" name="@Strings.AppName" entry="MadreApp" launcherIcon="@Drawables.LauncherIcon" minApiLevel="2.4.1">
        <!--
            Use the following from the Visual Studio Code comand palette to edit
            the build targets:
            "Monkey C: Set Products by Product Category" - Lets you add all products
                                       that belong to the same product category
            "Monkey C: Edit Products" - Lets you add or remove any product
        -->
        <iq:products>
            <iq:product id="instinct3amoled45mm"/>
            <iq:product id="instinct3amoled50mm"/>
            <iq:product id="instinct3solar45mm"/>
        </iq:products>
        <!--
            Use "Monkey C: Edit Permissions" from the Visual Studio Code command
            palette to update permissions.
        -->
        <iq:permissions>
            <iq:uses-permission id="Positioning"/>
            <iq:uses-permission id="UserProfile"/>
        </iq:permissions>
        <!--
            Use "Monkey C: Edit Languages" from the Visual Studio Code command
            palette to edit your compatible language list.
        -->
         <iq:languages>
            <!-- Add additional languages as needed -->
            <iq:language>eng</iq:language>
            <!-- <iq:language>fra</iq:language> -->
            <!-- <iq:language>spa</iq:language> -->
        </iq:languages>
        <!--
            Use "Monkey C: Configure Monkey Barrel" from the Visual Studio Code
            command palette to edit the included barrels.
        -->
        <iq:barrels/>
    </iq:application>
</iq:manifest>
