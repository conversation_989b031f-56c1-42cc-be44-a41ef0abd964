import Toybox.ActivityMonitor;
import Toybox.System;
import Toybox.Time;
import Toybox.Time.Gregorian;
import Toybox.Lang;
import Toybox.Communications;
import Toybox.UserProfile;

// Data management for smartwatch interface
class DataManager {

    // Get formatted time string
    static function getTimeString() as String {
        var clockTime = System.getClockTime();
        var is24Hour = System.getDeviceSettings().is24Hour;

        if (is24Hour) {
            return Lang.format("$1$:$2$", [
                clockTime.hour.format("%02d"),
                clockTime.min.format("%02d")
            ]);
        } else {
            var hour = clockTime.hour;
            if (hour > 12) {
                hour = hour - 12;
            } else if (hour == 0) {
                hour = 12;
            }
            return Lang.format("$1$:$2$", [
                hour.format("%d"),
                clockTime.min.format("%02d")
            ]);
        }
    }

    // Get day of week abbreviation
    static function getDayString() as String {
        var date = Gregorian.info(Time.now(), Time.FORMAT_MEDIUM);
        var dayNames = ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"];
        return dayNames[date.day_of_week - 1];
    }

    // Get formatted date string
    static function getDateString() as String {
        var date = Gregorian.info(Time.now(), Time.FORMAT_MEDIUM);
        var monthNames = ["JAN", "FEB", "MAR", "APR", "MAY", "JUN",
                         "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"];
        return Lang.format("$1$ $2$", [
            monthNames[date.month - 1],
            date.day
        ]);
    }

    // Get current heart rate
    static function getHeartRate() as Number? {
        var activityInfo = ActivityMonitor.getInfo();
        if (activityInfo has :currentHeartRate && activityInfo.currentHeartRate != null) {
            return activityInfo.currentHeartRate;
        }
        return null;
    }

    // Get steps count
    static function getSteps() as Number? {
        var activityInfo = ActivityMonitor.getInfo();
        if (activityInfo has :steps && activityInfo.steps != null) {
            return activityInfo.steps;
        }
        return null;
    }

    // Get steps progress (0.0 to 1.0)
    static function getStepsProgress() as Float {
        var activityInfo = ActivityMonitor.getInfo();
        if (activityInfo has :steps && activityInfo.steps != null &&
            activityInfo has :stepGoal && activityInfo.stepGoal != null &&
            activityInfo.stepGoal > 0) {
            var progress = activityInfo.steps.toFloat() / activityInfo.stepGoal.toFloat();
            return progress > 1.0 ? 1.0 : progress;
        }
        return 0.0;
    }

    // Get calories burned
    static function getCalories() as Number? {
        var activityInfo = ActivityMonitor.getInfo();
        if (activityInfo has :calories && activityInfo.calories != null) {
            return activityInfo.calories;
        }
        return null;
    }

    // Get battery level (0-100)
    static function getBatteryLevel() as Float {
        return System.getSystemStats().battery;
    }

    // Get battery color based on level
    static function getBatteryColor(level as Float) as Number {
        if (level > 50) {
            return 0x00FF00; // Green
        } else if (level > 20) {
            return 0xFFFF00; // Yellow
        } else {
            return 0xFF0000; // Red
        }
    }

    // Check Bluetooth connection status
    static function isBluetoothConnected() as Boolean {
        var deviceSettings = System.getDeviceSettings();
        if (deviceSettings has :phoneConnected) {
            return deviceSettings.phoneConnected;
        }
        return false;
    }

    // Get notification count (simulated for now)
    static function getNotificationCount() as Number {
        var deviceSettings = System.getDeviceSettings();
        if (deviceSettings has :notificationCount && deviceSettings.notificationCount != null) {
            return deviceSettings.notificationCount;
        }
        // Return a simulated count for demonstration
        return 99;
    }

    // Format large numbers with appropriate suffixes
    static function formatLargeNumber(number as Number) as String {
        if (number >= 10000) {
            var thousands = number / 1000;
            if (thousands >= 10) {
                return Lang.format("$1$k", [thousands.format("%.0f")]);
            } else {
                return Lang.format("$1$k", [thousands.format("%.1f")]);
            }
        }
        return number.toString();
    }

    // Get heart rate zone color
    static function getHeartRateZoneColor(heartRate as Number?) as Number {
        if (heartRate == null) {
            return 0x808080; // Gray for no data
        }

        var userProfile = Toybox.UserProfile.getProfile();
        var maxHR = 220;
        if (userProfile != null && userProfile has :maxHeartRate && userProfile.maxHeartRate != null) {
            maxHR = userProfile.maxHeartRate;
        } else if (userProfile != null && userProfile has :birthYear && userProfile.birthYear != null) {
            var age = Time.Gregorian.info(Time.now(), Time.FORMAT_SHORT).year - userProfile.birthYear;
            maxHR = 220 - age;
        }

        var percentage = (heartRate.toFloat() / maxHR) * 100;

        if (percentage < 50) {
            return 0x00FF00; // Green - Easy
        } else if (percentage < 60) {
            return 0xFFFF00; // Yellow - Moderate
        } else if (percentage < 70) {
            return 0xFF8000; // Orange - Vigorous
        } else if (percentage < 85) {
            return 0xFF4500; // Red-Orange - Hard
        } else {
            return 0xFF0000; // Red - Maximum
        }
    }
}
