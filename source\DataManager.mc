import Toybox.ActivityMonitor;
import Toybox.System;
import Toybox.Time;
import Toybox.Time.Gregorian;
import Toybox.Lang;
import Toybox.Communications;
import Toybox.UserProfile;

// Data management for smartwatch interface
class DataManager {

    // Get formatted time string
    static function getTimeString() as String {
        DebugUtils.enter("DataManager", "getTimeString");

        var clockTime = System.getClockTime();
        var is24Hour = System.getDeviceSettings().is24Hour;

        DebugUtils.logVar("DataManager", "hour", clockTime.hour);
        DebugUtils.logVar("DataManager", "minute", clockTime.min);
        DebugUtils.logVar("DataManager", "is24Hour", is24Hour);

        var timeString;
        if (is24Hour) {
            timeString = Lang.format("$1$:$2$", [
                clockTime.hour.format("%02d"),
                clockTime.min.format("%02d")
            ]);
        } else {
            var hour = clockTime.hour;
            if (hour > 12) {
                hour = hour - 12;
            } else if (hour == 0) {
                hour = 12;
            }
            timeString = Lang.format("$1$:$2$", [
                hour.format("%d"),
                clockTime.min.format("%02d")
            ]);
        }

        DebugUtils.logVar("DataManager", "timeString", timeString);
        DebugUtils.exit("DataManager", "getTimeString");
        return timeString;
    }

    // Get day of week abbreviation
    static function getDayString() as String {
        DebugUtils.enter("DataManager", "getDayString");

        var date = Gregorian.info(Time.now(), Time.FORMAT_MEDIUM);
        var dayNames = ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"];
        var dayString = dayNames[date.day_of_week - 1];

        DebugUtils.logVar("DataManager", "day_of_week", date.day_of_week);
        DebugUtils.logVar("DataManager", "dayString", dayString);
        DebugUtils.exit("DataManager", "getDayString");
        return dayString;
    }

    // Get formatted date string
    static function getDateString() as String {
        DebugUtils.enter("DataManager", "getDateString");

        var date = Gregorian.info(Time.now(), Time.FORMAT_MEDIUM);
        var monthNames = ["JAN", "FEB", "MAR", "APR", "MAY", "JUN",
                         "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"];
        var dateString = Lang.format("$1$ $2$", [
            monthNames[date.month - 1],
            date.day
        ]);

        DebugUtils.logVar("DataManager", "month", date.month);
        DebugUtils.logVar("DataManager", "day", date.day);
        DebugUtils.logVar("DataManager", "dateString", dateString);
        DebugUtils.exit("DataManager", "getDateString");
        return dateString;
    }

    // Get current heart rate
    static function getHeartRate() as Number? {
        DebugUtils.enter("DataManager", "getHeartRate");

        var activityInfo = ActivityMonitor.getInfo();
        var heartRate = null;

        if (activityInfo has :currentHeartRate && activityInfo.currentHeartRate != null) {
            heartRate = activityInfo.currentHeartRate;
        }

        DebugUtils.logVar("DataManager", "heartRate", heartRate);
        DebugUtils.exit("DataManager", "getHeartRate");
        return heartRate;
    }

    // Get steps count
    static function getSteps() as Number? {
        DebugUtils.enter("DataManager", "getSteps");

        var activityInfo = ActivityMonitor.getInfo();
        var steps = null;

        if (activityInfo has :steps && activityInfo.steps != null) {
            steps = activityInfo.steps;
        }

        DebugUtils.logVar("DataManager", "steps", steps);
        DebugUtils.exit("DataManager", "getSteps");
        return steps;
    }

    // Get steps progress (0.0 to 1.0)
    static function getStepsProgress() as Float {
        DebugUtils.enter("DataManager", "getStepsProgress");

        var activityInfo = ActivityMonitor.getInfo();
        var progress = 0.0;

        if (activityInfo has :steps && activityInfo.steps != null &&
            activityInfo has :stepGoal && activityInfo.stepGoal != null &&
            activityInfo.stepGoal > 0) {
            progress = activityInfo.steps.toFloat() / activityInfo.stepGoal.toFloat();
            progress = progress > 1.0 ? 1.0 : progress;

            DebugUtils.logVar("DataManager", "steps", activityInfo.steps);
            DebugUtils.logVar("DataManager", "stepGoal", activityInfo.stepGoal);
        }

        DebugUtils.logVar("DataManager", "stepsProgress", progress);
        DebugUtils.exit("DataManager", "getStepsProgress");
        return progress;
    }

    // Get calories burned
    static function getCalories() as Number? {
        DebugUtils.enter("DataManager", "getCalories");

        var activityInfo = ActivityMonitor.getInfo();
        var calories = null;

        if (activityInfo has :calories && activityInfo.calories != null) {
            calories = activityInfo.calories;
        }

        DebugUtils.logVar("DataManager", "calories", calories);
        DebugUtils.exit("DataManager", "getCalories");
        return calories;
    }

    // Get battery level (0-100)
    static function getBatteryLevel() as Float {
        DebugUtils.enter("DataManager", "getBatteryLevel");

        var batteryLevel = System.getSystemStats().battery;

        DebugUtils.logVar("DataManager", "batteryLevel", batteryLevel);
        DebugUtils.exit("DataManager", "getBatteryLevel");
        return batteryLevel;
    }

    // Get battery color based on level
    static function getBatteryColor(level as Float) as Number {
        DebugUtils.enter("DataManager", "getBatteryColor");
        DebugUtils.logVar("DataManager", "level", level);

        var color;
        if (level > 50) {
            color = 0x00FF00; // Green
        } else if (level > 20) {
            color = 0xFFFF00; // Yellow
        } else {
            color = 0xFF0000; // Red
        }

        DebugUtils.logColor("DataManager", "batteryColor", color);
        DebugUtils.exit("DataManager", "getBatteryColor");
        return color;
    }

    // Check Bluetooth connection status
    static function isBluetoothConnected() as Boolean {
        DebugUtils.enter("DataManager", "isBluetoothConnected");

        var deviceSettings = System.getDeviceSettings();
        var isConnected = false;

        if (deviceSettings has :phoneConnected) {
            isConnected = deviceSettings.phoneConnected;
        }

        DebugUtils.logVar("DataManager", "isBluetoothConnected", isConnected);
        DebugUtils.exit("DataManager", "isBluetoothConnected");
        return isConnected;
    }

    // Get notification count (simulated for now)
    static function getNotificationCount() as Number {
        DebugUtils.enter("DataManager", "getNotificationCount");

        var deviceSettings = System.getDeviceSettings();
        var count = 99; // Default simulated count

        if (deviceSettings has :notificationCount && deviceSettings.notificationCount != null) {
            count = deviceSettings.notificationCount;
        }

        DebugUtils.logVar("DataManager", "notificationCount", count);
        DebugUtils.exit("DataManager", "getNotificationCount");
        return count;
    }

    // Format large numbers with appropriate suffixes
    static function formatLargeNumber(number as Number) as String {
        DebugUtils.enter("DataManager", "formatLargeNumber");
        DebugUtils.logVar("DataManager", "inputNumber", number);

        var formattedNumber;
        if (number >= 10000) {
            var thousands = number / 1000;
            if (thousands >= 10) {
                formattedNumber = Lang.format("$1$k", [thousands.format("%.0f")]);
            } else {
                formattedNumber = Lang.format("$1$k", [thousands.format("%.1f")]);
            }
        } else {
            formattedNumber = number.toString();
        }

        DebugUtils.logVar("DataManager", "formattedNumber", formattedNumber);
        DebugUtils.exit("DataManager", "formatLargeNumber");
        return formattedNumber;
    }

    // Get heart rate zone color
    static function getHeartRateZoneColor(heartRate as Number?) as Number {
        DebugUtils.enter("DataManager", "getHeartRateZoneColor");
        DebugUtils.logVar("DataManager", "heartRate", heartRate);

        if (heartRate == null) {
            DebugUtils.info("DataManager", "No heart rate data, returning gray");
            DebugUtils.exit("DataManager", "getHeartRateZoneColor");
            return 0x808080; // Gray for no data
        }

        var userProfile = Toybox.UserProfile.getProfile();
        var maxHR = 220;

        if (userProfile != null && userProfile has :maxHeartRate && userProfile.maxHeartRate != null) {
            maxHR = userProfile.maxHeartRate;
            DebugUtils.info("DataManager", "Using profile max HR");
        } else if (userProfile != null && userProfile has :birthYear && userProfile.birthYear != null) {
            var age = Time.Gregorian.info(Time.now(), Time.FORMAT_SHORT).year - userProfile.birthYear;
            maxHR = 220 - age;
            DebugUtils.logVar("DataManager", "calculatedAge", age);
            DebugUtils.info("DataManager", "Calculated max HR from age");
        }

        DebugUtils.logVar("DataManager", "maxHR", maxHR);

        var percentage = (heartRate.toFloat() / maxHR) * 100;
        DebugUtils.logVar("DataManager", "hrPercentage", percentage);

        var color;
        if (percentage < 50) {
            color = 0x00FF00; // Green - Easy
        } else if (percentage < 60) {
            color = 0xFFFF00; // Yellow - Moderate
        } else if (percentage < 70) {
            color = 0xFF8000; // Orange - Vigorous
        } else if (percentage < 85) {
            color = 0xFF4500; // Red-Orange - Hard
        } else {
            color = 0xFF0000; // Red - Maximum
        }

        DebugUtils.logColor("DataManager", "hrZoneColor", color);
        DebugUtils.exit("DataManager", "getHeartRateZoneColor");
        return color;
    }
}
