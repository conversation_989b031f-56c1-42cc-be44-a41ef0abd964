import Toybox.Graphics;
import Toybox.Lang;

// Layout management for smartwatch interface positioning
class LayoutManager {
    private var _screenWidth as Number;
    private var _screenHeight as Number;
    private var _centerX as Number;
    private var _centerY as Number;

    function initialize(screenWidth as Number, screenHeight as Number) {
        _screenWidth = screenWidth;
        _screenHeight = screenHeight;
        _centerX = screenWidth / 2;
        _centerY = screenHeight / 2;
    }

    // Get time position (large central display)
    function getTimePosition() as Array<Number> {
        return [_centerX, _centerY - 20];
    }

    // Get day indicator position (top left with diagonal stripes)
    function getDayPosition() as Dictionary {
        var width = 60;
        var height = 40;
        return {
            :x => 20,
            :y => 30,
            :width => width,
            :height => height,
            :textX => 20 + width / 2,
            :textY => 30 + height / 2
        };
    }

    // Get heart rate position (top right with circular indicator)
    function getHeartRatePosition() as Dictionary {
        var radius = 25;
        var x = _screenWidth - 50;
        var y = 50;
        return {
            :centerX => x,
            :centerY => y,
            :radius => radius,
            :textX => x,
            :textY => y
        };
    }

    // Get date position (bottom center)
    function getDatePosition() as Array<Number> {
        return [_centerX, _screenHeight - 60];
    }

    // Get steps position (right side)
    function getStepsPosition() as Dictionary {
        return {
            :x => _screenWidth - 80,
            :y => _centerY + 20,
            :iconX => _screenWidth - 25,
            :iconY => _centerY + 15
        };
    }

    // Get calories position (right side, below steps)
    function getCaloriesPosition() as Dictionary {
        return {
            :x => _screenWidth - 80,
            :y => _centerY + 50,
            :iconX => _screenWidth - 25,
            :iconY => _centerY + 45
        };
    }

    // Get messages position (right side, below calories)
    function getMessagesPosition() as Dictionary {
        return {
            :x => _screenWidth - 80,
            :y => _centerY + 80,
            :badgeX => _screenWidth - 25,
            :badgeY => _centerY + 75
        };
    }

    // Get battery position (bottom left)
    function getBatteryPosition() as Dictionary {
        var width = 30;
        var height = 15;
        return {
            :x => 20,
            :y => _screenHeight - 40,
            :width => width,
            :height => height,
            :textX => 20,
            :textY => _screenHeight - 50
        };
    }

    // Get Bluetooth position (top left, near day)
    function getBluetoothPosition() as Dictionary {
        return {
            :x => 90,
            :y => 35,
            :size => 16
        };
    }

    // Get time font size based on screen size
    function getTimeFontSize() as Number {
        if (_screenWidth < 200) {
            return 48;
        } else if (_screenWidth < 300) {
            return 60;
        } else {
            return 72;
        }
    }

    // Get data font size
    function getDataFontSize() as Number {
        if (_screenWidth < 200) {
            return 18;
        } else if (_screenWidth < 300) {
            return 22;
        } else {
            return 24;
        }
    }

    // Get small font size
    function getSmallFontSize() as Number {
        if (_screenWidth < 200) {
            return 14;
        } else if (_screenWidth < 300) {
            return 16;
        } else {
            return 18;
        }
    }

    // Get screen dimensions
    function getScreenWidth() as Number {
        return _screenWidth;
    }

    function getScreenHeight() as Number {
        return _screenHeight;
    }

    function getCenterX() as Number {
        return _centerX;
    }

    function getCenterY() as Number {
        return _centerY;
    }

    // Calculate responsive spacing
    function getSpacing() as Number {
        return _screenWidth < 200 ? 15 : 20;
    }

    // Get margin size
    function getMargin() as Number {
        return _screenWidth < 200 ? 10 : 15;
    }
}
