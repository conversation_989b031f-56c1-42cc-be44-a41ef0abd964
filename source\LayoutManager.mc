import Toybox.Graphics;
import Toybox.Lang;

// Layout management for smartwatch interface positioning
class LayoutManager {
    private var _screenWidth as Number;
    private var _screenHeight as Number;
    private var _centerX as Number;
    private var _centerY as Number;

    function initialize(screenWidth as Number, screenHeight as Number) {
        DebugUtils.enter("LayoutManager", "initialize");
        DebugUtils.logVar("LayoutManager", "screenWidth", screenWidth);
        DebugUtils.logVar("LayoutManager", "screenHeight", screenHeight);

        _screenWidth = screenWidth;
        _screenHeight = screenHeight;
        _centerX = screenWidth / 2;
        _centerY = screenHeight / 2;

        DebugUtils.logVar("LayoutManager", "centerX", _centerX);
        DebugUtils.logVar("LayoutManager", "centerY", _centerY);
        DebugUtils.exit("LayoutManager", "initialize");
    }

    // Get time position (large central display)
    function getTimePosition() as Array<Number> {
        DebugUtils.enter("LayoutManager", "getTimePosition");

        var position = [_centerX, _centerY - 20];

        DebugUtils.logPosition("LayoutManager", "timePosition", position[0], position[1]);
        DebugUtils.exit("LayoutManager", "getTimePosition");
        return position;
    }

    // Get day indicator position (top left with diagonal stripes)
    function getDayPosition() as Dictionary {
        DebugUtils.enter("LayoutManager", "getDayPosition");

        var width = 60;
        var height = 40;
        var position = {
            :x => 20,
            :y => 30,
            :width => width,
            :height => height,
            :textX => 20 + width / 2,
            :textY => 30 + height / 2
        };

        DebugUtils.logDict("LayoutManager", "dayPosition", position);
        DebugUtils.exit("LayoutManager", "getDayPosition");
        return position;
    }

    // Get heart rate position (top right with circular indicator)
    function getHeartRatePosition() as Dictionary {
        DebugUtils.enter("LayoutManager", "getHeartRatePosition");

        var radius = 25;
        var x = _screenWidth - 50;
        var y = 50;
        var position = {
            :centerX => x,
            :centerY => y,
            :radius => radius,
            :textX => x,
            :textY => y
        };

        DebugUtils.logDict("LayoutManager", "heartRatePosition", position);
        DebugUtils.exit("LayoutManager", "getHeartRatePosition");
        return position;
    }

    // Get date position (bottom center)
    function getDatePosition() as Array<Number> {
        DebugUtils.enter("LayoutManager", "getDatePosition");

        var position = [_centerX, _screenHeight - 60];

        DebugUtils.logPosition("LayoutManager", "datePosition", position[0], position[1]);
        DebugUtils.exit("LayoutManager", "getDatePosition");
        return position;
    }

    // Get steps position (right side)
    function getStepsPosition() as Dictionary {
        DebugUtils.enter("LayoutManager", "getStepsPosition");

        var position = {
            :x => _screenWidth - 80,
            :y => _centerY + 20,
            :iconX => _screenWidth - 25,
            :iconY => _centerY + 15
        };

        DebugUtils.logDict("LayoutManager", "stepsPosition", position);
        DebugUtils.exit("LayoutManager", "getStepsPosition");
        return position;
    }

    // Get calories position (right side, below steps)
    function getCaloriesPosition() as Dictionary {
        DebugUtils.enter("LayoutManager", "getCaloriesPosition");

        var position = {
            :x => _screenWidth - 80,
            :y => _centerY + 50,
            :iconX => _screenWidth - 25,
            :iconY => _centerY + 45
        };

        DebugUtils.logDict("LayoutManager", "caloriesPosition", position);
        DebugUtils.exit("LayoutManager", "getCaloriesPosition");
        return position;
    }

    // Get messages position (right side, below calories)
    function getMessagesPosition() as Dictionary {
        DebugUtils.enter("LayoutManager", "getMessagesPosition");

        var position = {
            :x => _screenWidth - 80,
            :y => _centerY + 80,
            :badgeX => _screenWidth - 25,
            :badgeY => _centerY + 75
        };

        DebugUtils.logDict("LayoutManager", "messagesPosition", position);
        DebugUtils.exit("LayoutManager", "getMessagesPosition");
        return position;
    }

    // Get battery position (bottom left)
    function getBatteryPosition() as Dictionary {
        DebugUtils.enter("LayoutManager", "getBatteryPosition");

        var width = 30;
        var height = 15;
        var position = {
            :x => 20,
            :y => _screenHeight - 40,
            :width => width,
            :height => height,
            :textX => 20,
            :textY => _screenHeight - 50
        };

        DebugUtils.logDict("LayoutManager", "batteryPosition", position);
        DebugUtils.exit("LayoutManager", "getBatteryPosition");
        return position;
    }

    // Get Bluetooth position (top left, near day)
    function getBluetoothPosition() as Dictionary {
        DebugUtils.enter("LayoutManager", "getBluetoothPosition");

        var position = {
            :x => 90,
            :y => 35,
            :size => 16
        };

        DebugUtils.logDict("LayoutManager", "bluetoothPosition", position);
        DebugUtils.exit("LayoutManager", "getBluetoothPosition");
        return position;
    }

    // Get time font size based on screen size
    function getTimeFontSize() as Number {
        DebugUtils.enter("LayoutManager", "getTimeFontSize");

        var fontSize;
        if (_screenWidth < 200) {
            fontSize = 48;
        } else if (_screenWidth < 300) {
            fontSize = 60;
        } else {
            fontSize = 72;
        }

        DebugUtils.logVar("LayoutManager", "timeFontSize", fontSize);
        DebugUtils.exit("LayoutManager", "getTimeFontSize");
        return fontSize;
    }

    // Get data font size
    function getDataFontSize() as Number {
        DebugUtils.enter("LayoutManager", "getDataFontSize");

        var fontSize;
        if (_screenWidth < 200) {
            fontSize = 18;
        } else if (_screenWidth < 300) {
            fontSize = 22;
        } else {
            fontSize = 24;
        }

        DebugUtils.logVar("LayoutManager", "dataFontSize", fontSize);
        DebugUtils.exit("LayoutManager", "getDataFontSize");
        return fontSize;
    }

    // Get small font size
    function getSmallFontSize() as Number {
        DebugUtils.enter("LayoutManager", "getSmallFontSize");

        var fontSize;
        if (_screenWidth < 200) {
            fontSize = 14;
        } else if (_screenWidth < 300) {
            fontSize = 16;
        } else {
            fontSize = 18;
        }

        DebugUtils.logVar("LayoutManager", "smallFontSize", fontSize);
        DebugUtils.exit("LayoutManager", "getSmallFontSize");
        return fontSize;
    }

    // Get screen dimensions
    function getScreenWidth() as Number {
        DebugUtils.logVar("LayoutManager", "screenWidth", _screenWidth);
        return _screenWidth;
    }

    function getScreenHeight() as Number {
        DebugUtils.logVar("LayoutManager", "screenHeight", _screenHeight);
        return _screenHeight;
    }

    function getCenterX() as Number {
        DebugUtils.logVar("LayoutManager", "centerX", _centerX);
        return _centerX;
    }

    function getCenterY() as Number {
        DebugUtils.logVar("LayoutManager", "centerY", _centerY);
        return _centerY;
    }

    // Calculate responsive spacing
    function getSpacing() as Number {
        DebugUtils.enter("LayoutManager", "getSpacing");

        var spacing = _screenWidth < 200 ? 15 : 20;

        DebugUtils.logVar("LayoutManager", "spacing", spacing);
        DebugUtils.exit("LayoutManager", "getSpacing");
        return spacing;
    }

    // Get margin size
    function getMargin() as Number {
        DebugUtils.enter("LayoutManager", "getMargin");

        var margin = _screenWidth < 200 ? 10 : 15;

        DebugUtils.logVar("LayoutManager", "margin", margin);
        DebugUtils.exit("LayoutManager", "getMargin");
        return margin;
    }
}
