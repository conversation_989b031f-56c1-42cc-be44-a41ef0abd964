import Toybox.Application;
import Toybox.Lang;
import Toybox.WatchUi;

class MadreApp extends Application.AppBase {

    function initialize() {
        DebugUtils.enter("MadreApp", "initialize");
        AppBase.initialize();
        DebugUtils.exit("MadreApp", "initialize");
    }

    // onStart() is called on application start up
    function onStart(state as Dictionary?) as Void {
        DebugUtils.enter("MadreApp", "onStart");
        DebugUtils.logVar("MadreApp", "state", state);
        DebugUtils.logMemoryUsage("MadreApp");
        DebugUtils.exit("MadreApp", "onStart");
    }

    // onStop() is called when your application is exiting
    function onStop(state as Dictionary?) as Void {
        DebugUtils.enter("MadreApp", "onStop");
        DebugUtils.logVar("MadreApp", "state", state);
        DebugUtils.logMemoryUsage("MadreApp");
        DebugUtils.exit("MadreApp", "onStop");
    }

    // Return the initial view of your application here
    function getInitialView() as [Views] or [Views, InputDelegates] {
        DebugUtils.enter("MadreApp", "getInitialView");
        var view = new MadreView();
        DebugUtils.info("MadreApp", "Created MadreView instance");
        DebugUtils.exit("MadreApp", "getInitialView");
        return [ view ] as Array;
    }

    // New app settings have been received so trigger a UI update
    function onSettingsChanged() as Void {
        DebugUtils.enter("MadreApp", "onSettingsChanged");
        DebugUtils.info("MadreApp", "Settings changed, requesting UI update");
        WatchUi.requestUpdate();
        DebugUtils.exit("MadreApp", "onSettingsChanged");
    }

}

function getApp() as MadreApp {
    return Application.getApp() as MadreApp;
}