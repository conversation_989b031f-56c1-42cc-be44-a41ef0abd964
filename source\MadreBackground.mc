import Toybox.Application;
import Toybox.Graphics;
import Toybox.Lang;
import Toybox.WatchUi;

class MadreBackground extends WatchUi.Drawable {

    function initialize() {
        var dictionary = {
            :identifier => "MadreBackground"
        };

        Drawable.initialize(dictionary);
    }

    function draw(dc as Graphics.Dc) as Void {
        // Get background color using modern API
        var backgroundColor = Graphics.COLOR_BLACK; // Default color

        // Try to get from Properties (modern way)
        if (Application has :Properties) {
            var bgColor = Application.Properties.getValue("BackgroundColor");
            if (bgColor != null) {
                backgroundColor = bgColor as Number;
            }
        }
        // Fallback to Storage if Properties not available
        else if (Application has :Storage) {
            var bgColor = Application.Storage.getValue("BackgroundColor");
            if (bgColor != null) {
                backgroundColor = bgColor as Number;
            }
        }

        // Set the background color then call to clear the screen
        dc.setColor(Graphics.COLOR_TRANSPARENT, backgroundColor);
        dc.clear();
    }
}