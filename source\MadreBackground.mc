import Toybox.Application;
import Toybox.Graphics;
import Toybox.Lang;
import Toybox.WatchUi;

class MadreBackground extends WatchUi.Drawable {

    function initialize() {
        DebugUtils.enter("MadreBackground", "initialize");

        var dictionary = {
            :identifier => "MadreBackground"
        };

        Drawable.initialize(dictionary);
        DebugUtils.info("MadreBackground", "Background drawable initialized");
        DebugUtils.exit("MadreBackground", "initialize");
    }

    function draw(dc as Graphics.Dc) as Void {
        DebugUtils.enter("MadreBackground", "draw");
        DebugUtils.logScreenInfo("MadreBackground", dc);

        // Get background color using modern API
        var backgroundColor = Graphics.COLOR_BLACK; // Default color
        DebugUtils.logColor("MadreBackground", "defaultBackgroundColor", backgroundColor);

        // Try to get from Properties (modern way)
        if (Application has :Properties) {
            var bgColor = Application.Properties.getValue("BackgroundColor");
            if (bgColor != null) {
                backgroundColor = bgColor as Number;
                DebugUtils.info("MadreBackground", "Using Properties background color");
            }
        }
        // Fallback to Storage if Properties not available
        else if (Application has :Storage) {
            var bgColor = Application.Storage.getValue("BackgroundColor");
            if (bgColor != null) {
                backgroundColor = bgColor as Number;
                DebugUtils.info("MadreBackground", "Using Storage background color");
            }
        }

        DebugUtils.logColor("MadreBackground", "finalBackgroundColor", backgroundColor);

        // Set the background color then call to clear the screen
        dc.setColor(Graphics.COLOR_TRANSPARENT, backgroundColor);
        dc.clear();

        DebugUtils.exit("MadreBackground", "draw");
    }
}