import Toybox.Graphics;
import Toybox.Lang;
import Toybox.System;
import Toybox.WatchUi;
import Toybox.ActivityMonitor;
import Toybox.Time;
import Toybox.Time.Gregorian;
import Toybox.UserProfile;
import Toybox.Math;
import Toybox.Communications;

class MadreView extends WatchUi.WatchFace {
    private var _layoutManager as LayoutManager?;
    private var _isAwake as Boolean = true;

    // Colors - Modern smartwatch theme
    private var _backgroundColor as Number = 0x000000;  // Black
    private var _foregroundColor as Number = 0xFFFFFF;  // White
    private var _accentColor as Number = 0xFF1493;      // Deep Pink
    private var _secondaryColor as Number = 0x808080;   // Gray

    // Fonts
    private var _timeFont as Graphics.FontDefinition = Graphics.FONT_LARGE;
    private var _dataFont as Graphics.FontDefinition = Graphics.FONT_MEDIUM;
    private var _smallFont as Graphics.FontDefinition = Graphics.FONT_SMALL;



    function initialize() {
        DebugUtils.enter("MadreView", "initialize");
        DebugUtils.logMemoryUsage("MadreView");
        WatchFace.initialize();
        DebugUtils.exit("MadreView", "initialize");
    }

    function onStart(state as Dictionary?) as Void {
        DebugUtils.enter("MadreView", "onStart");
        DebugUtils.logVar("MadreView", "state", state);
        DebugUtils.logMemoryUsage("MadreView");
        DebugUtils.exit("MadreView", "onStart");
    }

    // Load your resources here
    function onLayout(dc as Dc) as Void {
        DebugUtils.enter("MadreView", "onLayout");

        var screenWidth = dc.getWidth();
        var screenHeight = dc.getHeight();

        // Debug screen info
        DebugUtils.logScreenInfo("MadreView", dc);

        // Initialize layout manager
        _layoutManager = new LayoutManager(screenWidth, screenHeight);
        DebugUtils.info("MadreView", "LayoutManager initialized");

        DebugUtils.exit("MadreView", "onLayout");
    }

    // Called when this View is brought to the foreground. Restore
    // the state of this View and prepare it to be shown. This includes
    // loading resources into memory.
    function onShow() as Void {
    }

    // Update the view
    function onUpdate(dc as Dc) as Void {
        DebugUtils.enter("MadreView", "onUpdate");
        DebugUtils.startTimer("onUpdate");
        DebugUtils.logMemoryUsage("MadreView");

        // Clear screen
        dc.setColor(_backgroundColor, _backgroundColor);
        dc.clear();

        if (_layoutManager == null) {
            DebugUtils.error("MadreView", "LayoutManager is null - returning");
            DebugUtils.endTimer("onUpdate");
            DebugUtils.exit("MadreView", "onUpdate");
            return;
        }

        DebugUtils.logVar("MadreView", "isAwake", _isAwake);
        DebugUtils.info("MadreView", "Drawing watchface elements");

        // Draw main time (always visible)
        drawMainTime(dc);

        // Only show detailed information when the watch is awake
        if (_isAwake) {
            // Draw day indicator with diagonal stripes
            drawDayIndicator(dc);

            // Draw heart rate with circular progress
            drawHeartRateIndicator(dc);

            // Draw date
            drawDateDisplay(dc);

            // Draw activity data (steps, calories, messages)
            drawActivityData(dc);

            // Draw battery indicator
            drawBatteryIndicator(dc);

            // Draw Bluetooth status
            drawBluetoothIndicator(dc);
        }

        DebugUtils.endTimer("onUpdate");
        DebugUtils.exit("MadreView", "onUpdate");
    }

    // Called when this View is removed from the screen. Save the
    // state of this View here. This includes freeing resources from
    // memory.
    function onHide() as Void {
    }

    // The user has just looked at their watch. Timers and animations may be started here.
    function onExitSleep() as Void {
        _isAwake = true;
        WatchUi.requestUpdate();
    }

    // Terminate any active timers and prepare for slow updates.
    function onEnterSleep() as Void {
        _isAwake = false;
        WatchUi.requestUpdate();
    }

    // Draw main time display
    private function drawMainTime(dc as Graphics.Dc) as Void {
        DebugUtils.enter("MadreView", "drawMainTime");
        DebugUtils.startTimer("drawMainTime");

        var timeString = DataManager.getTimeString();
        var timePos = _layoutManager.getTimePosition();

        DebugUtils.logVar("MadreView", "timeString", timeString);
        DebugUtils.logPosition("MadreView", "timePosition", timePos[0], timePos[1]);
        DebugUtils.logColor("MadreView", "foregroundColor", _foregroundColor);

        dc.setColor(_foregroundColor, Graphics.COLOR_TRANSPARENT);
        dc.drawText(timePos[0], timePos[1], _timeFont, timeString,
                   Graphics.TEXT_JUSTIFY_CENTER | Graphics.TEXT_JUSTIFY_VCENTER);

        DebugUtils.endTimer("drawMainTime");
        DebugUtils.exit("MadreView", "drawMainTime");
    }

    // Draw day indicator with diagonal stripes background
    private function drawDayIndicator(dc as Graphics.Dc) as Void {
        DebugUtils.enter("MadreView", "drawDayIndicator");

        var dayPos = _layoutManager.getDayPosition();
        var dayString = DataManager.getDayString();

        DebugUtils.logVar("MadreView", "dayString", dayString);
        DebugUtils.logDict("MadreView", "dayPosition", dayPos);

        // Draw diagonal stripes background
        UIComponents.drawDiagonalStripes(dc, dayPos[:x], dayPos[:y],
                                       dayPos[:width], dayPos[:height], _secondaryColor);

        // Draw day text
        dc.setColor(_foregroundColor, Graphics.COLOR_TRANSPARENT);
        dc.drawText(dayPos[:textX], dayPos[:textY], _dataFont, dayString,
                   Graphics.TEXT_JUSTIFY_CENTER | Graphics.TEXT_JUSTIFY_VCENTER);

        DebugUtils.exit("MadreView", "drawDayIndicator");
    }

    // Draw date display
    private function drawDateDisplay(dc as Graphics.Dc) as Void {
        DebugUtils.enter("MadreView", "drawDateDisplay");

        var dateString = DataManager.getDateString();
        var datePos = _layoutManager.getDatePosition();

        DebugUtils.logVar("MadreView", "dateString", dateString);
        DebugUtils.logPosition("MadreView", "datePosition", datePos[0], datePos[1]);

        dc.setColor(_foregroundColor, Graphics.COLOR_TRANSPARENT);
        dc.drawText(datePos[0], datePos[1], _dataFont, dateString, Graphics.TEXT_JUSTIFY_CENTER);

        DebugUtils.exit("MadreView", "drawDateDisplay");
    }

    // Draw heart rate with circular indicator
    private function drawHeartRateIndicator(dc as Graphics.Dc) as Void {
        DebugUtils.enter("MadreView", "drawHeartRateIndicator");

        var heartRate = DataManager.getHeartRate();
        var hrPos = _layoutManager.getHeartRatePosition();

        DebugUtils.logVar("MadreView", "heartRate", heartRate);
        DebugUtils.logDict("MadreView", "heartRatePosition", hrPos);

        if (heartRate != null) {
            // Draw circular background
            var hrColor = DataManager.getHeartRateZoneColor(heartRate);
            DebugUtils.logColor("MadreView", "hrZoneColor", hrColor);

            UIComponents.drawCircularProgress(dc, hrPos[:centerX], hrPos[:centerY],
                                            hrPos[:radius], 1.0, _secondaryColor, hrColor, 3);

            // Draw heart rate value
            dc.setColor(_foregroundColor, Graphics.COLOR_TRANSPARENT);
            dc.drawText(hrPos[:textX], hrPos[:textY], _dataFont, heartRate.toString(),
                       Graphics.TEXT_JUSTIFY_CENTER | Graphics.TEXT_JUSTIFY_VCENTER);
        } else {
            DebugUtils.info("MadreView", "No heart rate data available");
        }

        DebugUtils.exit("MadreView", "drawHeartRateIndicator");
    }

    // Draw activity data (steps, calories, messages)
    private function drawActivityData(dc as Graphics.Dc) as Void {
        // Draw steps
        var steps = DataManager.getSteps();
        if (steps != null) {
            var stepsPos = _layoutManager.getStepsPosition();
            var stepsString = DataManager.formatLargeNumber(steps);

            dc.setColor(_foregroundColor, Graphics.COLOR_TRANSPARENT);
            dc.drawText(stepsPos[:x], stepsPos[:y], _smallFont, stepsString, Graphics.TEXT_JUSTIFY_RIGHT);

            // Steps icon (using simple text)
            dc.setColor(_accentColor, Graphics.COLOR_TRANSPARENT);
            dc.drawText(stepsPos[:iconX], stepsPos[:iconY], _smallFont, "S", Graphics.TEXT_JUSTIFY_CENTER);
        }

        // Draw calories
        var calories = DataManager.getCalories();
        if (calories != null) {
            var caloriesPos = _layoutManager.getCaloriesPosition();
            var caloriesString = DataManager.formatLargeNumber(calories);

            dc.setColor(_foregroundColor, Graphics.COLOR_TRANSPARENT);
            dc.drawText(caloriesPos[:x], caloriesPos[:y], _smallFont, caloriesString, Graphics.TEXT_JUSTIFY_RIGHT);

            // Calories icon (using simple text)
            dc.setColor(_accentColor, Graphics.COLOR_TRANSPARENT);
            dc.drawText(caloriesPos[:iconX], caloriesPos[:iconY], _smallFont, "C", Graphics.TEXT_JUSTIFY_CENTER);
        }

        // Draw messages
        var messageCount = DataManager.getNotificationCount();
        var messagesPos = _layoutManager.getMessagesPosition();

        UIComponents.drawNotificationBadge(dc, messagesPos[:badgeX], messagesPos[:badgeY],
                                         messageCount, 12, _accentColor, _foregroundColor, _smallFont);
    }

    // Draw battery indicator
    private function drawBatteryIndicator(dc as Graphics.Dc) as Void {
        var batteryLevel = DataManager.getBatteryLevel();
        var batteryPos = _layoutManager.getBatteryPosition();
        var batteryColor = DataManager.getBatteryColor(batteryLevel);

        // Draw battery percentage text
        var batteryString = Lang.format("$1$%", [batteryLevel.format("%.0f")]);
        dc.setColor(_foregroundColor, Graphics.COLOR_TRANSPARENT);
        dc.drawText(batteryPos[:textX], batteryPos[:textY], _smallFont, batteryString, Graphics.TEXT_JUSTIFY_LEFT);

        // Draw battery icon
        UIComponents.drawBatteryIndicator(dc, batteryPos[:x], batteryPos[:y], batteryLevel,
                                        batteryPos[:width], batteryPos[:height],
                                        _secondaryColor, batteryColor);
    }

    // Draw Bluetooth status indicator
    private function drawBluetoothIndicator(dc as Graphics.Dc) as Void {
        var isConnected = DataManager.isBluetoothConnected();
        var btPos = _layoutManager.getBluetoothPosition();
        var btColor = isConnected ? _accentColor : _secondaryColor;

        UIComponents.drawBluetoothIndicator(dc, btPos[:x], btPos[:y], isConnected,
                                          btPos[:size], btColor, _secondaryColor);
    }

}
