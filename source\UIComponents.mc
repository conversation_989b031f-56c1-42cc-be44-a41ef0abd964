import Toybox.Graphics;
import Toybox.Lang;
import Toybox.Math;

// UI Components for modern smartwatch interface
class UIComponents {

    // Draw diagonal stripe pattern background
    static function drawDiagonalStripes(dc as Graphics.Dc, x as Number, y as Number,
                                      width as Number, height as Number,
                                      color as Number) as Void {
        DebugUtils.enter("UIComponents", "drawDiagonalStripes");
        DebugUtils.logPosition("UIComponents", "stripeArea", x, y);
        DebugUtils.logVar("UIComponents", "width", width);
        DebugUtils.logVar("UIComponents", "height", height);
        DebugUtils.logColor("UIComponents", "stripeColor", color);

        dc.setColor(color, Graphics.COLOR_TRANSPARENT);
        dc.setPenWidth(2);

        var stripeSpacing = 8;
        var startX = x - height; // Start from outside to cover the area

        for (var i = startX; i < x + width + height; i += stripeSpacing) {
            dc.drawLine(i, y, i + height, y + height);
        }

        DebugUtils.exit("UIComponents", "drawDiagonalStripes");
    }

    // Draw circular progress indicator
    static function drawCircularProgress(dc as Graphics.Dc, centerX as Number, centerY as Number,
                                       radius as Number, progress as Float,
                                       backgroundColor as Number, progressColor as Number,
                                       penWidth as Number) as Void {
        DebugUtils.enter("UIComponents", "drawCircularProgress");
        DebugUtils.logPosition("UIComponents", "progressCenter", centerX, centerY);
        DebugUtils.logVar("UIComponents", "radius", radius);
        DebugUtils.logVar("UIComponents", "progress", progress);
        DebugUtils.logColor("UIComponents", "backgroundColor", backgroundColor);
        DebugUtils.logColor("UIComponents", "progressColor", progressColor);
        DebugUtils.logVar("UIComponents", "penWidth", penWidth);

        dc.setPenWidth(penWidth);

        // Draw background circle
        dc.setColor(backgroundColor, Graphics.COLOR_TRANSPARENT);
        drawCircle(dc, centerX, centerY, radius);

        // Draw progress arc
        if (progress > 0) {
            dc.setColor(progressColor, Graphics.COLOR_TRANSPARENT);
            var sweepAngle = 360 * progress;
            DebugUtils.logVar("UIComponents", "sweepAngle", sweepAngle);
            drawArc(dc, centerX, centerY, radius, -90, sweepAngle.toNumber());
        }

        DebugUtils.exit("UIComponents", "drawCircularProgress");
    }

    // Draw a complete circle using line segments
    static function drawCircle(dc as Graphics.Dc, centerX as Number, centerY as Number,
                             radius as Number) as Void {
        drawArc(dc, centerX, centerY, radius, 0, 360);
    }

    // Draw arc using line segments
    static function drawArc(dc as Graphics.Dc, centerX as Number, centerY as Number,
                          radius as Number, startAngle as Number, sweepAngle as Number) as Void {
        var segments = Math.ceil(sweepAngle / 10).toNumber(); // More segments for smoother arcs
        if (segments < 4) { segments = 4; }

        var angleStep = sweepAngle.toFloat() / segments;

        for (var i = 0; i < segments; i++) {
            var angle1 = Math.toRadians(startAngle + i * angleStep);
            var angle2 = Math.toRadians(startAngle + (i + 1) * angleStep);

            var x1 = centerX + radius * Math.cos(angle1);
            var y1 = centerY + radius * Math.sin(angle1);
            var x2 = centerX + radius * Math.cos(angle2);
            var y2 = centerY + radius * Math.sin(angle2);

            dc.drawLine(x1, y1, x2, y2);
        }
    }

    // Draw rounded rectangle
    static function drawRoundedRect(dc as Graphics.Dc, x as Number, y as Number,
                                  width as Number, height as Number,
                                  cornerRadius as Number, color as Number) as Void {
        dc.setColor(color, Graphics.COLOR_TRANSPARENT);

        // Draw main rectangle
        dc.fillRectangle(x + cornerRadius, y, width - 2 * cornerRadius, height);
        dc.fillRectangle(x, y + cornerRadius, width, height - 2 * cornerRadius);

        // Draw corner circles
        dc.fillCircle(x + cornerRadius, y + cornerRadius, cornerRadius);
        dc.fillCircle(x + width - cornerRadius, y + cornerRadius, cornerRadius);
        dc.fillCircle(x + cornerRadius, y + height - cornerRadius, cornerRadius);
        dc.fillCircle(x + width - cornerRadius, y + height - cornerRadius, cornerRadius);
    }

    // Draw battery indicator with modern styling
    static function drawBatteryIndicator(dc as Graphics.Dc, x as Number, y as Number,
                                       batteryLevel as Float, width as Number, height as Number,
                                       backgroundColor as Number, fillColor as Number) as Void {
        DebugUtils.enter("UIComponents", "drawBatteryIndicator");
        DebugUtils.logPosition("UIComponents", "batteryPosition", x, y);
        DebugUtils.logVar("UIComponents", "batteryLevel", batteryLevel);
        DebugUtils.logVar("UIComponents", "width", width);
        DebugUtils.logVar("UIComponents", "height", height);
        DebugUtils.logColor("UIComponents", "backgroundColor", backgroundColor);
        DebugUtils.logColor("UIComponents", "fillColor", fillColor);

        dc.setPenWidth(2);

        // Battery body outline
        dc.setColor(backgroundColor, Graphics.COLOR_TRANSPARENT);
        dc.drawRoundedRectangle(x, y, width, height, 2);

        // Battery tip
        var tipWidth = 3;
        var tipHeight = height / 3;
        var tipX = x + width;
        var tipY = y + (height - tipHeight) / 2;
        dc.fillRectangle(tipX, tipY, tipWidth, tipHeight);

        // Battery fill
        var fillWidth = (width - 4) * batteryLevel / 100;
        DebugUtils.logVar("UIComponents", "fillWidth", fillWidth);
        if (fillWidth > 0) {
            dc.setColor(fillColor, Graphics.COLOR_TRANSPARENT);
            dc.fillRoundedRectangle(x + 2, y + 2, fillWidth, height - 4, 1);
        }

        DebugUtils.exit("UIComponents", "drawBatteryIndicator");
    }

    // Draw Bluetooth indicator
    static function drawBluetoothIndicator(dc as Graphics.Dc, x as Number, y as Number,
                                         isConnected as Boolean, size as Number,
                                         connectedColor as Number, disconnectedColor as Number) as Void {
        DebugUtils.enter("UIComponents", "drawBluetoothIndicator");
        DebugUtils.logPosition("UIComponents", "bluetoothPosition", x, y);
        DebugUtils.logVar("UIComponents", "isConnected", isConnected);
        DebugUtils.logVar("UIComponents", "size", size);
        DebugUtils.logColor("UIComponents", "connectedColor", connectedColor);
        DebugUtils.logColor("UIComponents", "disconnectedColor", disconnectedColor);

        var color = isConnected ? connectedColor : disconnectedColor;
        DebugUtils.logColor("UIComponents", "selectedColor", color);
        dc.setColor(color, Graphics.COLOR_TRANSPARENT);
        dc.setPenWidth(2);

        // Simple Bluetooth symbol approximation
        var halfSize = size / 2;

        // Vertical line
        dc.drawLine(x, y, x, y + size);

        // Upper triangle
        dc.drawLine(x, y, x + halfSize, y + halfSize / 2);
        dc.drawLine(x + halfSize, y + halfSize / 2, x, y + halfSize);

        // Lower triangle
        dc.drawLine(x, y + halfSize, x + halfSize, y + size - halfSize / 2);
        dc.drawLine(x + halfSize, y + size - halfSize / 2, x, y + size);

        DebugUtils.exit("UIComponents", "drawBluetoothIndicator");
    }

    // Draw notification indicator
    static function drawNotificationBadge(dc as Graphics.Dc, x as Number, y as Number,
                                        count as Number, radius as Number,
                                        backgroundColor as Number, textColor as Number,
                                        font as Graphics.FontDefinition) as Void {
        DebugUtils.enter("UIComponents", "drawNotificationBadge");
        DebugUtils.logPosition("UIComponents", "badgePosition", x, y);
        DebugUtils.logVar("UIComponents", "count", count);
        DebugUtils.logVar("UIComponents", "radius", radius);
        DebugUtils.logColor("UIComponents", "backgroundColor", backgroundColor);
        DebugUtils.logColor("UIComponents", "textColor", textColor);

        // Draw circle background
        dc.setColor(backgroundColor, Graphics.COLOR_TRANSPARENT);
        dc.fillCircle(x, y, radius);

        // Draw count text
        var countText = count > 99 ? "99+" : count.toString();
        DebugUtils.logVar("UIComponents", "countText", countText);
        dc.setColor(textColor, Graphics.COLOR_TRANSPARENT);
        dc.drawText(x, y, font, countText, Graphics.TEXT_JUSTIFY_CENTER | Graphics.TEXT_JUSTIFY_VCENTER);

        DebugUtils.exit("UIComponents", "drawNotificationBadge");
    }
}
