import Toybox.Application.Storage;
import Toybox.Graphics;
import Toybox.WatchUi;

class WatchFaceSettingsMenu extends WatchUi.Menu2 {

    function initialize() {
        DebugUtils.enter("WatchFaceSettingsMenu", "initialize");

        Menu2.initialize({:title=>"Settings"});

        Menu2.addItem(new WatchUi.MenuItem(
            "Theme",
            "Light/Dark",
            "theme",
            null
        ));

        Menu2.addItem(new WatchUi.MenuItem(
            "Show Seconds",
            "On/Off",
            "seconds",
            null
        ));

        Menu2.addItem(new WatchUi.MenuItem(
            "Data Fields",
            "Customize",
            "datafields",
            null
        ));

        DebugUtils.info("WatchFaceSettingsMenu", "Settings menu initialized with 3 items");
        DebugUtils.exit("WatchFaceSettingsMenu", "initialize");
    }
}

class WatchFaceSettingsDelegate extends WatchUi.Menu2InputDelegate {

    function initialize() {
        DebugUtils.enter("WatchFaceSettingsDelegate", "initialize");
        Menu2InputDelegate.initialize();
        DebugUtils.exit("WatchFaceSettingsDelegate", "initialize");
    }

    function onSelect(item as WatchUi.MenuItem) as Void {
        DebugUtils.enter("WatchFaceSettingsDelegate", "onSelect");

        var id = item.getId();
        DebugUtils.logVar("WatchFaceSettingsDelegate", "selectedId", id);

        if (id.equals("theme")) {
            DebugUtils.info("WatchFaceSettingsDelegate", "Theme toggle selected");
            toggleTheme();
        } else if (id.equals("seconds")) {
            DebugUtils.info("WatchFaceSettingsDelegate", "Seconds toggle selected");
            toggleSeconds();
        } else if (id.equals("datafields")) {
            DebugUtils.info("WatchFaceSettingsDelegate", "Data fields customization selected");
            customizeDataFields();
        }

        WatchUi.popView(WatchUi.SLIDE_IMMEDIATE);
        DebugUtils.exit("WatchFaceSettingsDelegate", "onSelect");
    }

    private function toggleTheme() as Void {
        DebugUtils.enter("WatchFaceSettingsDelegate", "toggleTheme");

        var currentTheme = Storage.getValue("theme");
        if (currentTheme == null) {
            currentTheme = "dark";
        }
        DebugUtils.logVar("WatchFaceSettingsDelegate", "currentTheme", currentTheme);

        var newTheme = currentTheme.equals("dark") ? "light" : "dark";
        DebugUtils.logVar("WatchFaceSettingsDelegate", "newTheme", newTheme);
        Storage.setValue("theme", newTheme);

        DebugUtils.exit("WatchFaceSettingsDelegate", "toggleTheme");
    }

    private function toggleSeconds() as Void {
        DebugUtils.enter("WatchFaceSettingsDelegate", "toggleSeconds");

        var showSeconds = Storage.getValue("showSeconds");
        if (showSeconds == null) {
            showSeconds = false;
        }
        DebugUtils.logVar("WatchFaceSettingsDelegate", "currentShowSeconds", showSeconds);

        var newShowSeconds = !showSeconds;
        DebugUtils.logVar("WatchFaceSettingsDelegate", "newShowSeconds", newShowSeconds);
        Storage.setValue("showSeconds", newShowSeconds);

        DebugUtils.exit("WatchFaceSettingsDelegate", "toggleSeconds");
    }

    private function customizeDataFields() as Void {
        DebugUtils.enter("WatchFaceSettingsDelegate", "customizeDataFields");
        DebugUtils.info("WatchFaceSettingsDelegate", "Data field customization not yet implemented");
        // Implementation for data field customization
        // This would typically open another menu or dialog
        DebugUtils.exit("WatchFaceSettingsDelegate", "customizeDataFields");
    }
}
